"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ChevronLeft, ChevronRight, Plane, Building, Users } from "lucide-react"
import { MemberTravelDetails } from "@/lib/domains/travel-details/travel-details.types"
import { generateTempAvatar } from "@/lib/utils/avatar-utils"
import Image from "next/image"

interface TravelDetailsViewerProps {
  travelDetails: MemberTravelDetails[]
  attendeesDetails: Array<{ id: string; displayName: string; photoURL?: string }>
}

type ViewMode = "flight" | "accommodation"

export function TravelDetailsViewer({ travelDetails, attendeesDetails }: TravelDetailsViewerProps) {
  const [viewMode, setViewMode] = useState<ViewMode>("flight")
  const [currentIndex, setCurrentIndex] = useState(0)

  // Filter details based on view mode
  const filteredDetails = useMemo(() => {
    return travelDetails.filter((detail) => {
      if (viewMode === "flight") {
        return detail.flightImage
      } else {
        return detail.accommodationImage
      }
    })
  }, [travelDetails, viewMode])

  // Get user details for current member
  const getCurrentMemberDetails = useMemo(() => {
    if (filteredDetails.length === 0) return null
    const currentDetail = filteredDetails[currentIndex]
    const userDetail = attendeesDetails.find((user) => user.id === currentDetail.id)
    return {
      ...currentDetail,
      displayName: userDetail?.displayName || currentDetail.memberName,
      photoURL: userDetail?.photoURL,
    }
  }, [filteredDetails, currentIndex, attendeesDetails])

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : filteredDetails.length - 1))
  }

  const handleNext = () => {
    setCurrentIndex((prev) => (prev < filteredDetails.length - 1 ? prev + 1 : 0))
  }

  const handleModeChange = (mode: ViewMode) => {
    setViewMode(mode)
    setCurrentIndex(0)
  }

  // Count members with each type of image
  const flightCount = travelDetails.filter((detail) => detail.flightImage).length
  const accommodationCount = travelDetails.filter((detail) => detail.accommodationImage).length

  if (travelDetails.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Travel Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">
              No travel details uploaded yet. Be the first to share your flight or accommodation
              details!
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (filteredDetails.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Travel Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Mode Toggle */}
            <div className="flex gap-2 overflow-x-auto">
              <Button
                variant={viewMode === "flight" ? "default" : "outline"}
                size="sm"
                onClick={() => handleModeChange("flight")}
                className="flex items-center gap-1 sm:gap-2 whitespace-nowrap flex-shrink-0"
              >
                <Plane className="h-4 w-4" />
                <span className="hidden sm:inline">Flight Details</span>
                <span className="sm:hidden">Flight</span>
                {flightCount > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {flightCount}
                  </Badge>
                )}
              </Button>
              <Button
                variant={viewMode === "accommodation" ? "default" : "outline"}
                size="sm"
                onClick={() => handleModeChange("accommodation")}
                className="flex items-center gap-1 sm:gap-2 whitespace-nowrap flex-shrink-0"
              >
                <Building className="h-4 w-4" />
                <span className="hidden sm:inline">Accommodation</span>
                <span className="sm:hidden">Hotel</span>
                {accommodationCount > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {accommodationCount}
                  </Badge>
                )}
              </Button>
            </div>

            <div className="text-center py-8">
              {viewMode === "flight" ? (
                <>
                  <Plane className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No flight details uploaded yet.</p>
                </>
              ) : (
                <>
                  <Building className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No accommodation details uploaded yet.</p>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const currentMember = getCurrentMemberDetails

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Travel Details
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Mode Toggle */}
          <div className="flex gap-2 overflow-x-auto">
            <Button
              variant={viewMode === "flight" ? "default" : "outline"}
              size="sm"
              onClick={() => handleModeChange("flight")}
              className="flex items-center gap-1 sm:gap-2 whitespace-nowrap flex-shrink-0"
            >
              <Plane className="h-4 w-4" />
              <span className="hidden sm:inline">Flight Details</span>
              <span className="sm:hidden">Flight</span>
              {flightCount > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {flightCount}
                </Badge>
              )}
            </Button>
            <Button
              variant={viewMode === "accommodation" ? "default" : "outline"}
              size="sm"
              onClick={() => handleModeChange("accommodation")}
              className="flex items-center gap-1 sm:gap-2 whitespace-nowrap flex-shrink-0"
            >
              <Building className="h-4 w-4" />
              <span className="hidden sm:inline">Accommodation</span>
              <span className="sm:hidden">Hotel</span>
              {accommodationCount > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {accommodationCount}
                </Badge>
              )}
            </Button>
          </div>

          {/* Current Member Info */}
          {currentMember && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={currentMember.photoURL} />
                  <AvatarFallback>
                    <img
                      src={generateTempAvatar(currentMember.displayName)}
                      alt={currentMember.displayName}
                      className="h-full w-full"
                    />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-sm">{currentMember.displayName}</p>
                  <p className="text-xs text-muted-foreground">
                    {viewMode === "flight" ? "Flight Details" : "Accommodation Details"}
                  </p>
                </div>
              </div>

              {/* Navigation */}
              {filteredDetails.length > 1 && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrevious}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    {currentIndex + 1} of {filteredDetails.length}
                  </span>
                  <Button variant="outline" size="sm" onClick={handleNext} className="h-8 w-8 p-0">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Image Display */}
          {currentMember && (
            <div className="aspect-video relative overflow-hidden rounded-lg border">
              <Image
                src={
                  viewMode === "flight"
                    ? currentMember.flightImage || ""
                    : currentMember.accommodationImage || ""
                }
                alt={`${currentMember.displayName}'s ${viewMode} details`}
                fill
                className="object-cover"
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
