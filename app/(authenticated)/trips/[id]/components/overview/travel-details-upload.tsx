"use client"

import { useState, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Upload, Plane, Building, X, Loader2 } from "lucide-react"
import { useTravelDetails } from "@/lib/domains/travel-details/travel-details.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { toast } from "@/components/ui/use-toast"
import Image from "next/image"

interface TravelDetailsUploadProps {
  tripId: string
  memberName: string
  onUploadComplete?: () => void
}

export function TravelDetailsUpload({
  tripId,
  memberName,
  onUploadComplete,
}: TravelDetailsUploadProps) {
  const user = useUser()
  const { uploadImage, isUploading, getMemberDetails } = useTravelDetails()

  const [flightPreview, setFlightPreview] = useState<string | null>(null)
  const [accommodationPreview, setAccommodationPreview] = useState<string | null>(null)

  const flightInputRef = useRef<HTMLInputElement>(null)
  const accommodationInputRef = useRef<HTMLInputElement>(null)

  // Get existing member details
  const memberDetails = getMemberDetails(user?.uid || "")

  const handleFileSelect = useCallback((file: File, type: "flight" | "accommodation") => {
    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"]
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: "Please select a JPEG, PNG, or WebP image.",
        variant: "destructive",
      })
      return
    }

    // Validate file size (5MB)
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      toast({
        title: "File too large",
        description: "Please select an image smaller than 5MB.",
        variant: "destructive",
      })
      return
    }

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      if (type === "flight") {
        setFlightPreview(result)
      } else {
        setAccommodationPreview(result)
      }
    }
    reader.readAsDataURL(file)
  }, [])

  const handleUpload = useCallback(
    async (file: File, type: "flight" | "accommodation") => {
      if (!user?.uid) {
        toast({
          title: "Authentication required",
          description: "Please log in to upload images.",
          variant: "destructive",
        })
        return
      }

      const success = await uploadImage(tripId, user?.uid || "", memberName, { file, type })

      if (success) {
        // Clear preview
        if (type === "flight") {
          setFlightPreview(null)
        } else {
          setAccommodationPreview(null)
        }

        // Reset file input
        if (type === "flight" && flightInputRef.current) {
          flightInputRef.current.value = ""
        } else if (type === "accommodation" && accommodationInputRef.current) {
          accommodationInputRef.current.value = ""
        }

        onUploadComplete?.()
      }
    },
    [user?.uid, uploadImage, tripId, memberName, onUploadComplete]
  )

  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, type: "flight" | "accommodation") => {
      const file = event.target.files?.[0]
      if (file) {
        handleFileSelect(file, type)
      }
    },
    [handleFileSelect]
  )

  const handleUploadClick = useCallback(
    (type: "flight" | "accommodation") => {
      const inputRef = type === "flight" ? flightInputRef : accommodationInputRef
      const preview = type === "flight" ? flightPreview : accommodationPreview

      if (preview) {
        // Upload the selected file
        const input = inputRef.current
        const file = input?.files?.[0]
        if (file) {
          handleUpload(file, type)
        }
      } else {
        // Open file picker
        inputRef.current?.click()
      }
    },
    [flightPreview, accommodationPreview, handleUpload]
  )

  const clearPreview = useCallback((type: "flight" | "accommodation") => {
    if (type === "flight") {
      setFlightPreview(null)
      if (flightInputRef.current) {
        flightInputRef.current.value = ""
      }
    } else {
      setAccommodationPreview(null)
      if (accommodationInputRef.current) {
        accommodationInputRef.current.value = ""
      }
    }
  }, [])

  const isUploadingFlight = isUploading(user?.uid || "")
  const isUploadingAccommodation = isUploading(user?.uid || "")

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Your Travel Details
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Flight Upload */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2 text-sm font-medium">
            <Plane className="h-4 w-4" />
            Flight Details
          </Label>

          <input
            ref={flightInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => handleFileChange(e, "flight")}
            className="hidden"
          />

          {flightPreview || memberDetails?.flightImage ? (
            <div className="relative">
              <div className="aspect-video relative overflow-hidden rounded-lg border">
                <Image
                  src={flightPreview || memberDetails?.flightImage || ""}
                  alt="Flight details"
                  fill
                  className="object-cover"
                />
                {flightPreview && (
                  <button
                    onClick={() => clearPreview("flight")}
                    className="absolute top-2 right-2 p-1 bg-black/50 text-white rounded-full hover:bg-black/70"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
              {flightPreview && (
                <Button
                  onClick={() => handleUploadClick("flight")}
                  disabled={isUploadingFlight}
                  className="w-full mt-2"
                >
                  {isUploadingFlight ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    "Upload Flight Image"
                  )}
                </Button>
              )}
            </div>
          ) : (
            <div
              onClick={() => handleUploadClick("flight")}
              className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
            >
              <Plane className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">Click to upload flight details image</p>
              <p className="text-xs text-muted-foreground mt-1">PNG, JPG, WebP up to 5MB</p>
            </div>
          )}
        </div>

        {/* Accommodation Upload */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2 text-sm font-medium">
            <Building className="h-4 w-4" />
            Accommodation Details
          </Label>

          <input
            ref={accommodationInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => handleFileChange(e, "accommodation")}
            className="hidden"
          />

          {accommodationPreview || memberDetails?.accommodationImage ? (
            <div className="relative">
              <div className="aspect-video relative overflow-hidden rounded-lg border">
                <Image
                  src={accommodationPreview || memberDetails?.accommodationImage || ""}
                  alt="Accommodation details"
                  fill
                  className="object-cover"
                />
                {accommodationPreview && (
                  <button
                    onClick={() => clearPreview("accommodation")}
                    className="absolute top-2 right-2 p-1 bg-black/50 text-white rounded-full hover:bg-black/70"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
              {accommodationPreview && (
                <Button
                  onClick={() => handleUploadClick("accommodation")}
                  disabled={isUploadingAccommodation}
                  className="w-full mt-2"
                >
                  {isUploadingAccommodation ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    "Upload Accommodation Image"
                  )}
                </Button>
              )}
            </div>
          ) : (
            <div
              onClick={() => handleUploadClick("accommodation")}
              className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
            >
              <Building className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">
                Click to upload accommodation details image
              </p>
              <p className="text-xs text-muted-foreground mt-1">PNG, JPG, WebP up to 5MB</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
